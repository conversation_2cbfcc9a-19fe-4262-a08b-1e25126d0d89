package tracing

import (
	"context"

	gmw "github.com/Laisky/gin-middlewares/v6"
	"github.com/<PERSON>sky/zap"
	"github.com/gin-gonic/gin"

	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/model"
)

// GetTraceID extracts the TraceID from gin context using gin-middlewares
func GetTraceID(c *gin.Context) string {
	traceID, err := gmw.TraceID(c)
	if err != nil {
		logger.Logger.Warn("failed to get trace ID from gin-middlewares", zap.Error(err))
		// Fallback to empty string - this should not happen in normal operation
		return ""
	}
	return traceID.String()
}

// GetTraceIDFromContext extracts TraceID from standard context
// This is useful when we only have context.Context and not gin.Context
func GetTraceIDFromContext(ctx context.Context) string {
	if ginCtx, ok := gmw.GetGinCtxFromStdCtx(ctx); ok {
		return GetTraceID(ginCtx)
	}
	logger.Logger.Warn("failed to get gin context from standard context for trace ID extraction")
	return ""
}

// RecordTraceStart creates a new trace record when a request starts
func RecordTraceStart(c *gin.Context) {
	traceID := GetTraceID(c)
	if traceID == "" {
		logger.Logger.Warn("empty trace ID, skipping trace record creation")
		return
	}

	url := c.Request.URL.String()
	method := c.Request.Method
	bodySize := c.Request.ContentLength
	if bodySize < 0 {
		bodySize = 0
	}

	ctx := c.Request.Context()
	_, err := model.CreateTrace(ctx, traceID, url, method, bodySize)
	if err != nil {
		logger.Logger.Error("failed to create trace record",
			zap.Error(err),
			zap.String("trace_id", traceID))
	}
}

// RecordTraceTimestamp updates a specific timestamp in the trace record
func RecordTraceTimestamp(c *gin.Context, timestampKey string) {
	traceID := GetTraceID(c)
	if traceID == "" {
		logger.Logger.Warn("empty trace ID, skipping timestamp update",
			zap.String("timestamp_key", timestampKey))
		return
	}

	ctx := c.Request.Context()
	err := model.UpdateTraceTimestamp(ctx, traceID, timestampKey)
	if err != nil {
		logger.Logger.Error("failed to update trace timestamp",
			zap.Error(err),
			zap.String("trace_id", traceID),
			zap.String("timestamp_key", timestampKey))
	}
}

// RecordTraceTimestampFromContext updates a timestamp using standard context
func RecordTraceTimestampFromContext(ctx context.Context, timestampKey string) {
	traceID := GetTraceIDFromContext(ctx)
	if traceID == "" {
		logger.Logger.Warn("empty trace ID from context, skipping timestamp update",
			zap.String("timestamp_key", timestampKey))
		return
	}

	err := model.UpdateTraceTimestamp(ctx, traceID, timestampKey)
	if err != nil {
		logger.Logger.Error("failed to update trace timestamp from context",
			zap.Error(err),
			zap.String("trace_id", traceID),
			zap.String("timestamp_key", timestampKey))
	}
}

// RecordTraceStatus updates the HTTP status code for a trace
func RecordTraceStatus(c *gin.Context, status int) {
	traceID := GetTraceID(c)
	if traceID == "" {
		logger.Logger.Warn("empty trace ID, skipping status update",
			zap.Int("status", status))
		return
	}

	ctx := c.Request.Context()
	err := model.UpdateTraceStatus(ctx, traceID, status)
	if err != nil {
		logger.Logger.Error("failed to update trace status",
			zap.Error(err),
			zap.String("trace_id", traceID),
			zap.Int("status", status))
	}
}

// RecordTraceEnd marks the completion of a request and records final timestamp
func RecordTraceEnd(c *gin.Context) {
	traceID := GetTraceID(c)
	if traceID == "" {
		logger.Logger.Warn("empty trace ID, skipping trace end recording")
		return
	}

	// Record the final timestamp
	RecordTraceTimestamp(c, model.TimestampRequestCompleted)

	// Record the final status code
	status := c.Writer.Status()
	if status == 0 {
		status = 200 // Default to 200 if no status was set
	}
	RecordTraceStatus(c, status)
}

// WithTraceID adds trace ID to structured logging fields
func WithTraceID(c *gin.Context, fields ...zap.Field) []zap.Field {
	traceID := GetTraceID(c)
	if traceID == "" {
		return fields
	}

	traceField := zap.String("trace_id", traceID)
	return append([]zap.Field{traceField}, fields...)
}

// WithTraceIDFromContext adds trace ID to structured logging fields from context
func WithTraceIDFromContext(ctx context.Context, fields ...zap.Field) []zap.Field {
	traceID := GetTraceIDFromContext(ctx)
	if traceID == "" {
		return fields
	}

	traceField := zap.String("trace_id", traceID)
	return append([]zap.Field{traceField}, fields...)
}
