{"name": "one-api-modern", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build --emptyOutDir", "build:prod": "tsc -b && vite build --mode production --emptyOutDir", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.0.0", "@tanstack/react-table": "^8.10.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^0.2.1", "i18next": "^23.7.0", "i18next-browser-languagedetector": "^7.2.0", "lucide-react": "^0.294.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.62.0", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.0", "recharts": "^2.12.7", "tailwind-merge": "^2.6.0", "zod": "^3.25.76", "zustand": "^4.4.0"}, "devDependencies": {"@eslint/js": "^9.32.0", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^14.3.1", "@types/node": "^24.2.1", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^3.4.17", "tailwindcss-animate": "^1.0.7", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.1.0", "vitest": "^1.6.1"}}